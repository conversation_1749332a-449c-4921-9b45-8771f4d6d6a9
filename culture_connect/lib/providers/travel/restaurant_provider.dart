import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/models/travel/restaurant.dart';
import 'package:culture_connect/models/location/geo_location.dart';

/// Provider for a list of sample restaurants
final restaurantsProvider = Provider<List<Restaurant>>((ref) {
  return _getSampleRestaurants();
});

/// Provider for a specific restaurant by ID
final restaurantByIdProvider = Provider.family<Restaurant?, String>((ref, id) {
  final restaurants = ref.watch(restaurantsProvider);
  try {
    return restaurants.firstWhere((restaurant) => restaurant.id == id);
  } catch (e) {
    return null;
  }
});

/// Provider for featured restaurants
final featuredRestaurantsProvider = Provider<List<Restaurant>>((ref) {
  final restaurants = ref.watch(restaurantsProvider);
  return restaurants.where((restaurant) => restaurant.isFeatured).toList();
});

/// Provider for restaurants by cuisine type
final restaurantsByCuisineProvider =
    Provider.family<List<Restaurant>, CuisineType>((ref, cuisineType) {
  final restaurants = ref.watch(restaurantsProvider);
  return restaurants
      .where((restaurant) => restaurant.cuisineTypes.contains(cuisineType))
      .toList();
});

/// Get a list of sample restaurants
List<Restaurant> _getSampleRestaurants() {
  return [
    Restaurant(
      id: 'restaurant1',
      name: 'The Fancy Bistro',
      description:
          'An elegant bistro offering a fusion of French and Italian cuisine in a cozy atmosphere. Our chefs use only the freshest ingredients to create memorable dining experiences.',
      price: 150.0,
      currency: '\$',
      rating: 4.7,
      reviewCount: 128,
      imageUrl:
          'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      additionalImages: [
        'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        'https://images.unsplash.com/photo-**********-3ed3cdb5ed0c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        'https://images.unsplash.com/photo-1484659619207-9165d119dafe?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      ],
      provider: 'CultureConnect Dining',
      location: '123 Gourmet Street, Paris, France',
      coordinates: const GeoLocation(latitude: 48.8566, longitude: 2.3522),
      isAvailable: true,
      isFeatured: true,
      isOnSale: false,
      tags: ['Romantic', 'Fine Dining', 'Wine Selection'],
      amenities: ['Free Wi-Fi', 'Valet Parking', 'Outdoor Seating'],
      cancellationPolicy:
          'Free cancellation up to 24 hours before your reservation.',
      createdAt: DateTime.now().subtract(const Duration(days: 365)),
      updatedAt: DateTime.now().subtract(const Duration(days: 30)),
      restaurantType: RestaurantType.bistro,
      cuisineTypes: [
        CuisineType.french,
        CuisineType.italian,
        CuisineType.fusion
      ],
      menuItems: [
        const MenuItem(
          id: 'item1',
          name: 'Truffle Risotto',
          description:
              'Creamy Arborio rice with black truffle, parmesan, and wild mushrooms.',
          price: 28.0,
          currency: '\$',
          category: 'Main Course',
          isVegetarian: true,
          isVegan: false,
          isGlutenFree: false,
          containsNuts: false,
          isSpicy: false,
          isPopular: true,
          imageUrl:
              'https://images.unsplash.com/photo-1633964913295-ceb43826e7c7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
        const MenuItem(
          id: 'item2',
          name: 'Coq au Vin',
          description:
              'Traditional French dish of chicken braised with wine, lardons, mushrooms, and garlic.',
          price: 32.0,
          currency: '\$',
          category: 'Main Course',
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: true,
          containsNuts: false,
          isSpicy: false,
          imageUrl:
              'https://images.unsplash.com/photo-1600891963935-9e2e13096d9a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
        const MenuItem(
          id: 'item3',
          name: 'Escargot de Bourgogne',
          description: 'Snails baked in a shell with parsley garlic butter.',
          price: 18.0,
          currency: '\$',
          category: 'Appetizers',
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: true,
          containsNuts: false,
          isSpicy: false,
          imageUrl:
              'https://images.unsplash.com/photo-1625944525533-473f1a3d54e7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
        const MenuItem(
          id: 'item4',
          name: 'Crème Brûlée',
          description:
              'Classic French dessert featuring rich custard topped with caramelized sugar.',
          price: 12.0,
          currency: '\$',
          category: 'Desserts',
          isVegetarian: true,
          isVegan: false,
          isGlutenFree: true,
          containsNuts: false,
          isSpicy: false,
          isPopular: true,
          imageUrl:
              'https://images.unsplash.com/photo-1470124182917-cc6e71b22ecc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
      ],
      openingHours: {
        'Monday': '5:00 PM - 10:00 PM',
        'Tuesday': '5:00 PM - 10:00 PM',
        'Wednesday': '5:00 PM - 10:00 PM',
        'Thursday': '5:00 PM - 11:00 PM',
        'Friday': '5:00 PM - 11:00 PM',
        'Saturday': '4:00 PM - 11:00 PM',
        'Sunday': '4:00 PM - 9:00 PM',
      },
      hasOutdoorSeating: true,
      hasBar: true,
      hasLiveMusic: false,
      hasKidsMenu: false,
      hasVegetarianOptions: true,
      hasVeganOptions: true,
      hasGlutenFreeOptions: true,
      hasHalalOptions: false,
      hasKosherOptions: false,
      hasDressCode: true,
      dressCodeDescription: 'Smart casual. No shorts or flip-flops.',
      requiresReservation: true,
      hasParking: true,
      hasValetParking: true,
      isWheelchairAccessible: true,
      hasWifi: true,
      acceptsCreditCards: true,
      hasView: true,
      viewDescription: 'Scenic view of the Eiffel Tower.',
    ),
    Restaurant(
      id: 'restaurant2',
      name: 'Sushi Paradise',
      description:
          'Authentic Japanese sushi restaurant offering the freshest fish and traditional preparation methods. Our master chefs have over 20 years of experience.',
      price: 120.0,
      currency: '\$',
      rating: 4.9,
      reviewCount: 256,
      imageUrl:
          'https://images.unsplash.com/photo-1579871494447-9811cf80d66c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      additionalImages: [
        'https://images.unsplash.com/photo-**********-f6e147245754?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        'https://images.unsplash.com/photo-1617196034183-421b4917c92d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        'https://images.unsplash.com/photo-**********-64566f976cfa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      ],
      provider: 'CultureConnect Dining',
      location: '456 Ocean Avenue, Tokyo, Japan',
      coordinates: const GeoLocation(latitude: 35.6762, longitude: 139.6503),
      isAvailable: true,
      isFeatured: true,
      isOnSale: true,
      originalPrice: 150.0,
      discountPercentage: 20,
      tags: ['Japanese', 'Sushi', 'Fresh Fish'],
      amenities: ['Sake Bar', 'Private Dining Rooms', 'Chef\'s Table'],
      cancellationPolicy:
          'Free cancellation up to 12 hours before your reservation.',
      createdAt: DateTime.now().subtract(const Duration(days: 180)),
      updatedAt: DateTime.now().subtract(const Duration(days: 15)),
      restaurantType: RestaurantType.fineDining,
      cuisineTypes: [CuisineType.japanese, CuisineType.seafood],
      menuItems: [
        const MenuItem(
          id: 'item5',
          name: 'Omakase Sushi Set',
          description:
              'Chef\'s selection of the freshest fish and seafood, served with traditional accompaniments.',
          price: 85.0,
          currency: '\$',
          category: 'Chef\'s Specials',
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: false,
          containsNuts: false,
          isSpicy: false,
          isPopular: true,
          imageUrl:
              'https://images.unsplash.com/photo-1579584425555-c3ce17fd4351?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
        const MenuItem(
          id: 'item6',
          name: 'Dragon Roll',
          description: 'Eel and cucumber inside, avocado and tobiko on top.',
          price: 22.0,
          currency: '\$',
          category: 'Specialty Rolls',
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: false,
          containsNuts: false,
          isSpicy: false,
          imageUrl:
              'https://images.unsplash.com/photo-1617196035154-1e7e6e28b0db?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
        const MenuItem(
          id: 'item7',
          name: 'Spicy Tuna Roll',
          description: 'Fresh tuna mixed with spicy mayo and green onions.',
          price: 18.0,
          currency: '\$',
          category: 'Specialty Rolls',
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: false,
          containsNuts: false,
          isSpicy: true,
          isPopular: true,
          imageUrl:
              'https://images.unsplash.com/photo-1635343484272-8315e8b3e8b7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
        const MenuItem(
          id: 'item8',
          name: 'Vegetable Tempura',
          description:
              'Assorted seasonal vegetables lightly battered and fried.',
          price: 16.0,
          currency: '\$',
          category: 'Appetizers',
          isVegetarian: true,
          isVegan: true,
          isGlutenFree: false,
          containsNuts: false,
          isSpicy: false,
          imageUrl:
              'https://images.unsplash.com/photo-1615557960916-c616f8570d30?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
      ],
      openingHours: {
        'Monday': 'Closed',
        'Tuesday': '12:00 PM - 2:30 PM, 5:30 PM - 10:00 PM',
        'Wednesday': '12:00 PM - 2:30 PM, 5:30 PM - 10:00 PM',
        'Thursday': '12:00 PM - 2:30 PM, 5:30 PM - 10:00 PM',
        'Friday': '12:00 PM - 2:30 PM, 5:30 PM - 11:00 PM',
        'Saturday': '5:30 PM - 11:00 PM',
        'Sunday': '5:30 PM - 9:00 PM',
      },
      hasOutdoorSeating: false,
      hasBar: true,
      hasLiveMusic: false,
      hasKidsMenu: false,
      hasVegetarianOptions: true,
      hasVeganOptions: false,
      hasGlutenFreeOptions: true,
      hasHalalOptions: false,
      hasKosherOptions: false,
      hasDressCode: false,
      dressCodeDescription: null,
      requiresReservation: true,
      hasParking: true,
      hasValetParking: false,
      isWheelchairAccessible: true,
      hasWifi: true,
      acceptsCreditCards: true,
      hasView: false,
      viewDescription: null,
    ),
  ];
}
