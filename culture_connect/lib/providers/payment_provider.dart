import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/models/payment_method.dart';
import 'package:culture_connect/models/payment_transaction.dart';
import 'package:culture_connect/models/receipt.dart';
import 'package:culture_connect/services/payment_service.dart';

/// Provider for the payment service
final paymentServiceProvider = Provider<PaymentService>((ref) {
  return PaymentService();
});

/// Provider for saved payment methods (simplified)
final savedPaymentMethodsProvider =
    FutureProvider<List<PaymentMethod>>((ref) async {
  // Payment method management has been simplified
  // In a real implementation, this would load saved payment methods
  return <PaymentMethod>[];
});

/// Provider for the default payment method
final defaultPaymentMethodProvider =
    FutureProvider<PaymentMethod?>((ref) async {
  final paymentMethods = await ref.watch(savedPaymentMethodsProvider.future);

  if (paymentMethods.isEmpty) {
    return null;
  }

  return paymentMethods.firstWhere(
    (method) => method.isDefault,
    orElse: () => paymentMethods.first,
  );
});

/// Provider for transaction history
final transactionHistoryProvider =
    FutureProvider<List<PaymentTransaction>>((ref) async {
  final paymentService = ref.watch(paymentServiceProvider);
  return await paymentService.getTransactionHistory();
});

/// Provider for a specific transaction
final transactionProvider = FutureProvider.family<PaymentTransaction?, String>(
    (ref, transactionId) async {
  final paymentService = ref.watch(paymentServiceProvider);
  try {
    return await paymentService.getTransaction(transactionId);
  } catch (e) {
    return null;
  }
});

/// Provider for receipts
final receiptsProvider = FutureProvider<List<Receipt>>((ref) async {
  final paymentService = ref.watch(paymentServiceProvider);
  return await paymentService.getReceipts();
});

/// Provider for a specific receipt
final receiptProvider =
    FutureProvider.family<Receipt?, String>((ref, receiptId) async {
  final paymentService = ref.watch(paymentServiceProvider);
  return await paymentService.getReceipt(receiptId);
});

/// Provider for a receipt by transaction ID
final receiptByTransactionProvider =
    FutureProvider.family<Receipt?, String>((ref, transactionId) async {
  final paymentService = ref.watch(paymentServiceProvider);
  final receipts = await paymentService.getReceipts();

  try {
    return receipts.firstWhere(
      (receipt) => receipt.transactionId == transactionId,
    );
  } catch (e) {
    return null;
  }
});

/// State for payment processing
class PaymentProcessingState {
  /// Whether payment is in progress
  final bool isProcessing;

  /// Error message if payment failed
  final String? errorMessage;

  /// Transaction ID if payment succeeded
  final String? transactionId;

  /// Receipt ID if payment succeeded
  final String? receiptId;

  /// Creates a new payment processing state
  const PaymentProcessingState({
    this.isProcessing = false,
    this.errorMessage,
    this.transactionId,
    this.receiptId,
  });

  /// Creates a copy of this state with the given fields replaced with new values
  PaymentProcessingState copyWith({
    bool? isProcessing,
    String? errorMessage,
    String? transactionId,
    String? receiptId,
  }) {
    return PaymentProcessingState(
      isProcessing: isProcessing ?? this.isProcessing,
      errorMessage: errorMessage ?? this.errorMessage,
      transactionId: transactionId ?? this.transactionId,
      receiptId: receiptId ?? this.receiptId,
    );
  }

  /// Initial state
  static const initial = PaymentProcessingState();

  /// Processing state
  static const processing = PaymentProcessingState(isProcessing: true);

  /// Error state
  static PaymentProcessingState error(String message) {
    return PaymentProcessingState(
      isProcessing: false,
      errorMessage: message,
    );
  }

  /// Success state
  static PaymentProcessingState success({
    required String transactionId,
    String? receiptId,
  }) {
    return PaymentProcessingState(
      isProcessing: false,
      transactionId: transactionId,
      receiptId: receiptId,
    );
  }

  /// Whether the payment was successful
  bool get isSuccess => transactionId != null;

  /// Whether the payment failed
  bool get isError => errorMessage != null;
}

/// Provider for payment processing state
final paymentProcessingProvider =
    StateNotifierProvider<PaymentProcessingNotifier, PaymentProcessingState>(
        (ref) {
  return PaymentProcessingNotifier(ref);
});

/// Notifier for payment processing state
class PaymentProcessingNotifier extends StateNotifier<PaymentProcessingState> {
  final Ref _ref;

  /// Creates a new payment processing notifier
  PaymentProcessingNotifier(this._ref) : super(PaymentProcessingState.initial);

  /// Process a payment
  Future<void> processPayment({
    required String bookingId,
    required String experienceId,
    required double amount,
    required String paymentMethodId,
  }) async {
    state = PaymentProcessingState.processing;

    try {
      final paymentService = _ref.read(paymentServiceProvider);

      // Payment method management has been simplified
      // In a real implementation, this would look up the payment method by ID
      // PaymentMethod? paymentMethod; // TODO: Implement payment method lookup

      // Create a mock booking for demo purposes
      final mockBooking = Booking(
        id: 'mock-booking-${DateTime.now().millisecondsSinceEpoch}',
        experienceId: 'mock-experience',
        date: DateTime.now().add(const Duration(days: 3)),
        timeSlot: TimeSlot(
          startTime: DateTime.now().add(const Duration(days: 3, hours: 10)),
          endTime: DateTime.now().add(const Duration(days: 3, hours: 12)),
        ),
        participantCount: 2,
        totalAmount: amount,
        status: BookingStatus.confirmed,
        specialRequirements: '',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Process the payment
      final result = await paymentService.processPayment(
        booking: mockBooking,
        provider: PaymentProvider.stripe, // Default to Stripe
        userEmail: '<EMAIL>', // In a real app, get from user profile
        userName: 'John Doe', // In a real app, get from user profile
        // paymentMethodId: paymentMethod?.id, // TODO: Implement payment method selection
      );

      if (result.success) {
        state = PaymentProcessingState.success(
          transactionId: result.transactionId!,
          receiptId: result.additionalData?['receiptId'] as String?,
        );
      } else {
        state = PaymentProcessingState.error(
          result.errorMessage ?? 'Payment failed',
        );
      }
    } catch (e) {
      state = PaymentProcessingState.error(
        'Payment failed: $e',
      );
    }
  }

  /// Reset the payment state
  void reset() {
    state = PaymentProcessingState.initial;
  }
}
