import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/verification_model.dart';
import 'package:culture_connect/models/verification_document.dart';
import 'package:culture_connect/models/user_verification_level.dart';
import 'package:culture_connect/services/verification_service.dart';
import 'package:culture_connect/services/background_check_service.dart';
import 'package:culture_connect/providers/auth_provider.dart';

/// Provider for the user's verification status
class UserVerificationNotifier extends StateNotifier<UserVerificationStatus?> {
  final VerificationService _verificationService;
  final BackgroundCheckService _backgroundCheckService;

  /// Creates a new user verification notifier
  UserVerificationNotifier(
    this._verificationService,
    this._backgroundCheckService,
  ) : super(null) {
    // Load the user's verification status
    _loadVerificationStatus();
  }

  /// Load the user's verification status
  Future<void> _loadVerificationStatus() async {
    try {
      // Get the user's verification badges
      final badges = await _verificationService.getValidVerificationBadges();

      // Get the user's pending verification requests
      final requests = await _verificationService.getVerificationRequests();

      // Create the user verification status
      final status =
          UserVerificationStatus.fromBadgesAndRequests(badges, requests);

      // Update the state
      state = status;
    } catch (e) {
      debugPrint('Error loading verification status: $e');
    }
  }

  /// Refresh the user's verification status
  Future<void> refreshVerificationStatus() async {
    await _loadVerificationStatus();
  }

  /// Submit a verification request
  Future<VerificationRequest> submitVerificationRequest({
    required VerificationType type,
    required List<dynamic> documents,
    String? notes,
  }) async {
    final request = await _verificationService.submitVerificationRequest(
      type: type,
      documents: documents,
      notes: notes,
    );

    // Refresh the verification status
    await _loadVerificationStatus();

    return request;
  }

  /// Request a background check
  Future<void> requestBackgroundCheck({
    required BackgroundCheckProvider provider,
    required BackgroundCheckType type,
    required Map<String, dynamic> userData,
    required List<String> documentUrls,
  }) async {
    await _backgroundCheckService.requestBackgroundCheck(
      provider: provider,
      type: type,
      userData: userData,
      documentUrls: documentUrls,
    );

    // Refresh the verification status
    await _loadVerificationStatus();
  }

  /// Check if the user has a specific verification type
  bool hasVerification(VerificationType type) {
    if (state == null) return false;
    return state!.hasVerification(type);
  }

  /// Check if the user has a pending request for a specific verification type
  bool hasPendingRequest(VerificationType type) {
    if (state == null) return false;
    return state!.hasPendingRequest(type);
  }

  /// Get the verification badge for a specific type
  VerificationBadge? getBadge(VerificationType type) {
    if (state == null) return null;
    return state!.getBadge(type);
  }

  /// Get the pending request for a specific type
  VerificationRequest? getPendingRequest(VerificationType type) {
    if (state == null) return null;
    return state!.getPendingRequest(type);
  }

  /// Get the user's verification level
  UserVerificationLevel get verificationLevel {
    if (state == null) return UserVerificationLevel.unverified;
    return state!.level;
  }

  /// Check if the user can upgrade to the next level
  bool get canUpgrade {
    if (state == null) return false;
    return state!.canUpgrade;
  }

  /// Get the progress percentage towards the next level
  double get nextLevelProgress {
    if (state == null) return 0.0;
    return state!.nextLevelProgress;
  }

  /// Get the next verification type needed to reach the next level
  VerificationType? get nextVerificationType {
    if (state == null) return null;
    return state!.level.nextVerificationType;
  }

  /// Get all verification documents for the current user
  Future<List<VerificationDocument>> getUserDocuments() async {
    // Pass the userId to the service if needed in the future
    // Currently, the service uses its internal _userId
    return _verificationService.getUserDocuments();
  }
}

/// Provider for the user's verification status
final userVerificationProvider =
    StateNotifierProvider<UserVerificationNotifier, UserVerificationStatus?>(
        (ref) {
  final user = ref.watch(authStateProvider).user;
  if (user == null) {
    throw Exception('User must be logged in to access verification status');
  }

  final verificationService = ref.watch(verificationServiceProvider);
  final backgroundCheckService = ref.watch(backgroundCheckServiceProvider);

  return UserVerificationNotifier(
    verificationService,
    backgroundCheckService,
  );
});

/// Provider for the user's verification level
final userVerificationLevelProvider = Provider<UserVerificationLevel>((ref) {
  final verificationStatus = ref.watch(userVerificationProvider);
  if (verificationStatus == null) return UserVerificationLevel.unverified;
  return verificationStatus.level;
});

/// Provider for whether the user can upgrade to the next level
final canUpgradeVerificationProvider = Provider<bool>((ref) {
  final verificationStatus = ref.watch(userVerificationProvider);
  if (verificationStatus == null) return false;
  return verificationStatus.canUpgrade;
});

/// Provider for the progress towards the next level
final nextLevelProgressProvider = Provider<double>((ref) {
  final verificationStatus = ref.watch(userVerificationProvider);
  if (verificationStatus == null) return 0.0;
  return verificationStatus.nextLevelProgress;
});

/// Provider for the next verification type needed
final nextVerificationTypeProvider = Provider<VerificationType?>((ref) {
  final verificationStatus = ref.watch(userVerificationProvider);
  if (verificationStatus == null) return null;
  return verificationStatus.level.nextVerificationType;
});
