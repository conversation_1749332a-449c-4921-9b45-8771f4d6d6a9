import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';
import 'package:culture_connect/providers/voice_translation_provider.dart';
import 'package:culture_connect/utils/rtl_utils.dart';

/// Provider for the current text direction
final textDirectionProvider = Provider<TextDirection>((ref) {
  final sourceLanguage = ref.watch(sourceLanguageProvider);
  final targetLanguage = ref.watch(targetLanguageProvider);
  final useRTLOverride = ref.watch(useRTLOverrideProvider);
  final rtlOverrideDirection = ref.watch(rtlOverrideDirectionProvider);

  // If RTL override is enabled, use the override direction
  if (useRTLOverride) {
    return rtlOverrideDirection;
  }

  // Check if either source or target language is RTL
  final isSourceRTL = RTLUtils.isRTL(sourceLanguage.code);
  final isTargetRTL = RTLUtils.isRTL(targetLanguage.code);

  // If target language is RTL, use RTL direction
  if (isTargetRTL) {
    return TextDirection.rtl;
  }

  // If source language is RTL and target is not, use RTL direction for input
  if (isSourceRTL) {
    return TextDirection.rtl;
  }

  // Default to LTR
  return TextDirection.ltr;
});

/// Provider for whether to use RTL override
final useRTLOverrideProvider = StateNotifierProvider<BoolNotifier, bool>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  final initialValue = prefs.getBool('use_rtl_override') ?? false;

  return BoolNotifier(
    initialValue,
    onChanged: (value) async {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('use_rtl_override', value);
    },
  );
});

/// Provider for the RTL override direction
final rtlOverrideDirectionProvider =
    StateNotifierProvider<TextDirectionNotifier, TextDirection>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  final initialValue = prefs.getString('rtl_override_direction') == 'rtl'
      ? TextDirection.rtl
      : TextDirection.ltr;

  return TextDirectionNotifier(
    initialValue,
    onChanged: (value) async {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'rtl_override_direction',
        value == TextDirection.rtl ? 'rtl' : 'ltr',
      );
    },
  );
});

/// Provider for whether to automatically detect RTL languages
final autoDetectRTLProvider = StateNotifierProvider<BoolNotifier, bool>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  final initialValue = prefs.getBool('auto_detect_rtl') ?? true;

  return BoolNotifier(
    initialValue,
    onChanged: (value) async {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('auto_detect_rtl', value);
    },
  );
});

/// Provider for the text alignment based on the current text direction
final textAlignmentProvider = Provider<TextAlign>((ref) {
  final textDirection = ref.watch(textDirectionProvider);
  return textDirection == TextDirection.rtl ? TextAlign.right : TextAlign.left;
});

/// Provider for the start alignment based on the current text direction
final startAlignmentProvider = Provider<MainAxisAlignment>((ref) {
  final textDirection = ref.watch(textDirectionProvider);
  return textDirection == TextDirection.rtl
      ? MainAxisAlignment.end
      : MainAxisAlignment.start;
});

/// Provider for the end alignment based on the current text direction
final endAlignmentProvider = Provider<MainAxisAlignment>((ref) {
  final textDirection = ref.watch(textDirectionProvider);
  return textDirection == TextDirection.rtl
      ? MainAxisAlignment.start
      : MainAxisAlignment.end;
});

/// Notifier for boolean values
class BoolNotifier extends StateNotifier<bool> {
  final Future<void> Function(bool value)? onChanged;

  BoolNotifier(super.initialValue, {this.onChanged});

  void toggle() {
    state = !state;
    onChanged?.call(state);
  }

  void setValue(bool value) {
    state = value;
    onChanged?.call(state);
  }
}

/// Notifier for text direction values
class TextDirectionNotifier extends StateNotifier<TextDirection> {
  final Future<void> Function(TextDirection value)? onChanged;

  TextDirectionNotifier(super.initialValue, {this.onChanged});

  void toggle() {
    state = state == TextDirection.ltr ? TextDirection.rtl : TextDirection.ltr;
    onChanged?.call(state);
  }

  void setValue(TextDirection value) {
    state = value;
    onChanged?.call(state);
  }
}
