import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:culture_connect/models/group_chat_model.dart';
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/models/user_model.dart';
import 'package:culture_connect/providers/auth_provider.dart';

/// A provider for managing group chats
class GroupChatNotifier
    extends StateNotifier<AsyncValue<List<GroupChatModel>>> {
  final FirebaseFirestore _firestore;
  final FirebaseStorage _storage;
  final String _userId;

  GroupChatNotifier(
    this._firestore,
    this._storage,
    this._userId,
  ) : super(const AsyncValue.loading()) {
    _fetchGroupChats();
  }

  /// Fetches all group chats for the current user
  Future<void> _fetchGroupChats() async {
    try {
      final snapshot = await _firestore
          .collection('group_chats')
          .where('members.$_userId', isNull: false)
          .orderBy('lastMessageAt', descending: true)
          .get();

      final groupChats = snapshot.docs.map((doc) {
        final data = doc.data();
        return GroupChatModel.fromJson({...data, 'id': doc.id});
      }).toList();

      state = AsyncValue.data(groupChats);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Creates a new group chat
  Future<String> createGroupChat({
    required String name,
    required String description,
    File? image,
    required List<String> memberIds,
    bool isPublic = false,
    List<String> tags = const [],
  }) async {
    try {
      // Ensure the current user is included as an admin
      if (!memberIds.contains(_userId)) {
        memberIds.add(_userId);
      }

      // Create the group chat document
      final groupChatRef = _firestore.collection('group_chats').doc();
      final now = DateTime.now();

      // Upload group image if provided
      String? imageUrl;
      if (image != null) {
        final storageRef = _storage
            .ref()
            .child('group_chat_images')
            .child('${groupChatRef.id}_${now.millisecondsSinceEpoch}');

        final uploadTask = storageRef.putFile(image);
        final snapshot = await uploadTask.whenComplete(() {});
        imageUrl = await snapshot.ref.getDownloadURL();
      }

      // Create members map with the current user as admin
      final members = <String, Map<String, dynamic>>{};
      for (final memberId in memberIds) {
        members[memberId] = {
          'userId': memberId,
          'role': memberId == _userId ? 'admin' : 'member',
          'joinedAt': now,
          'isMuted': false,
        };
      }

      // Set group chat data
      await groupChatRef.set({
        'name': name,
        'description': description,
        'imageUrl': imageUrl,
        'members': members,
        'createdAt': now,
        'lastMessageAt': now,
        'lastMessageText': '',
        'lastMessageSenderId': '',
        'isActive': true,
        'isPublic': isPublic,
        'tags': tags,
      });

      // Create a system message announcing the group creation
      final message = MessageModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        chatId: groupChatRef.id,
        senderId: _userId,
        recipientId: '',
        text: 'Group created by $_userId',
        timestamp: now,
        status: MessageStatus.sent,
        type: MessageType.system,
        isGroupMessage: true,
      );

      await _firestore.collection('messages').add(message.toJson());

      // Update the group chat with the system message
      await groupChatRef.update({
        'lastMessageAt': now,
        'lastMessageText': 'Group created',
        'lastMessageSenderId': _userId,
      });

      _fetchGroupChats();
      return groupChatRef.id;
    } catch (error) {
      rethrow;
    }
  }

  /// Sends a message to a group chat
  Future<void> sendGroupMessage(MessageModel message) async {
    try {
      // Add message to Firestore
      await _firestore.collection('messages').add(message.toJson());

      // Update group chat with last message info
      await _firestore.collection('group_chats').doc(message.chatId).update({
        'lastMessageAt': message.timestamp,
        'lastMessageText': message.text,
        'lastMessageSenderId': message.senderId,
      });

      _fetchGroupChats();
    } catch (error) {
      rethrow;
    }
  }

  /// Sends a media message to a group chat
  Future<void> sendGroupMediaMessage(MessageModel message, File file) async {
    try {
      // Upload file to Firebase Storage
      final storageRef = _storage
          .ref()
          .child('chat_media')
          .child(message.chatId)
          .child(
              '${DateTime.now().millisecondsSinceEpoch}_${file.path.split('/').last}');

      final uploadTask = storageRef.putFile(file);
      final snapshot = await uploadTask.whenComplete(() {});
      final downloadUrl = await snapshot.ref.getDownloadURL();

      // Create updated message with media URL
      final updatedMessage = message.copyWith(
        mediaUrl: downloadUrl,
        status: MessageStatus.sent,
      );

      // Add message to Firestore
      await _firestore.collection('messages').add(updatedMessage.toJson());

      // Update group chat with last message info
      await _firestore.collection('group_chats').doc(message.chatId).update({
        'lastMessageAt': message.timestamp,
        'lastMessageText': message.type == MessageType.image
            ? '📷 Image'
            : message.type == MessageType.video
                ? '🎥 Video'
                : message.type == MessageType.audio
                    ? '🎵 Audio'
                    : 'File',
        'lastMessageSenderId': message.senderId,
      });

      _fetchGroupChats();
    } catch (error) {
      rethrow;
    }
  }

  /// Adds a user to a group chat
  Future<void> addMemberToGroup(String groupId, String userId,
      {GroupMemberRole role = GroupMemberRole.member}) async {
    try {
      final groupDoc =
          await _firestore.collection('group_chats').doc(groupId).get();

      if (!groupDoc.exists) {
        throw Exception('Group chat not found');
      }

      final group =
          GroupChatModel.fromJson({...groupDoc.data()!, 'id': groupDoc.id});

      // Check if the current user has permission to add members
      if (!group.isUserModerator(_userId)) {
        throw Exception(
            'You do not have permission to add members to this group');
      }

      // Check if the user is already a member
      if (group.isUserMember(userId)) {
        throw Exception('User is already a member of this group');
      }

      // Add the user to the group
      final now = DateTime.now();
      await _firestore.collection('group_chats').doc(groupId).update({
        'members.$userId': {
          'userId': userId,
          'role': role.toString().split('.').last,
          'joinedAt': now,
          'isMuted': false,
        },
      });

      // Create a system message announcing the new member
      final message = MessageModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        chatId: groupId,
        senderId: _userId,
        recipientId: '',
        text: '$userId was added to the group',
        timestamp: now,
        status: MessageStatus.sent,
        type: MessageType.system,
        isGroupMessage: true,
      );

      await _firestore.collection('messages').add(message.toJson());

      _fetchGroupChats();
    } catch (error) {
      rethrow;
    }
  }

  /// Removes a user from a group chat
  Future<void> removeMemberFromGroup(String groupId, String userId) async {
    try {
      final groupDoc =
          await _firestore.collection('group_chats').doc(groupId).get();

      if (!groupDoc.exists) {
        throw Exception('Group chat not found');
      }

      final group =
          GroupChatModel.fromJson({...groupDoc.data()!, 'id': groupDoc.id});

      // Check if the current user has permission to remove members
      if (!group.isUserModerator(_userId) && _userId != userId) {
        throw Exception(
            'You do not have permission to remove members from this group');
      }

      // Check if the user is a member
      if (!group.isUserMember(userId)) {
        throw Exception('User is not a member of this group');
      }

      // Check if the user is the last admin
      if (group.isUserAdmin(userId)) {
        final admins = group.members.values
            .where((m) => m.role == GroupMemberRole.admin)
            .toList();
        if (admins.length == 1 && admins.first.userId == userId) {
          throw Exception('Cannot remove the last admin from the group');
        }
      }

      // Remove the user from the group
      await _firestore.collection('group_chats').doc(groupId).update({
        'members.$userId': FieldValue.delete(),
      });

      // Create a system message announcing the member removal
      final now = DateTime.now();
      final message = MessageModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        chatId: groupId,
        senderId: _userId,
        recipientId: '',
        text: userId == _userId
            ? '$userId left the group'
            : '$userId was removed from the group',
        timestamp: now,
        status: MessageStatus.sent,
        type: MessageType.system,
        isGroupMessage: true,
      );

      await _firestore.collection('messages').add(message.toJson());

      _fetchGroupChats();
    } catch (error) {
      rethrow;
    }
  }

  /// Updates a user's role in a group chat
  Future<void> updateMemberRole(
      String groupId, String userId, GroupMemberRole newRole) async {
    try {
      final groupDoc =
          await _firestore.collection('group_chats').doc(groupId).get();

      if (!groupDoc.exists) {
        throw Exception('Group chat not found');
      }

      final group =
          GroupChatModel.fromJson({...groupDoc.data()!, 'id': groupDoc.id});

      // Check if the current user has permission to update roles
      if (!group.isUserAdmin(_userId)) {
        throw Exception('You do not have permission to update member roles');
      }

      // Check if the user is a member
      if (!group.isUserMember(userId)) {
        throw Exception('User is not a member of this group');
      }

      // Update the user's role
      await _firestore.collection('group_chats').doc(groupId).update({
        'members.$userId.role': newRole.toString().split('.').last,
      });

      // Create a system message announcing the role change
      final now = DateTime.now();
      final message = MessageModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        chatId: groupId,
        senderId: _userId,
        recipientId: '',
        text: '$userId is now a ${newRole.displayName}',
        timestamp: now,
        status: MessageStatus.sent,
        type: MessageType.system,
        isGroupMessage: true,
      );

      await _firestore.collection('messages').add(message.toJson());

      _fetchGroupChats();
    } catch (error) {
      rethrow;
    }
  }

  /// Updates a group chat's information
  Future<void> updateGroupChat({
    required String groupId,
    String? name,
    String? description,
    File? image,
    bool? isPublic,
    List<String>? tags,
  }) async {
    try {
      final groupDoc =
          await _firestore.collection('group_chats').doc(groupId).get();

      if (!groupDoc.exists) {
        throw Exception('Group chat not found');
      }

      final group =
          GroupChatModel.fromJson({...groupDoc.data()!, 'id': groupDoc.id});

      // Check if the current user has permission to update the group
      if (!group.isUserAdmin(_userId)) {
        throw Exception('You do not have permission to update this group');
      }

      // Prepare update data
      final updateData = <String, dynamic>{};

      if (name != null) {
        updateData['name'] = name;
      }

      if (description != null) {
        updateData['description'] = description;
      }

      if (isPublic != null) {
        updateData['isPublic'] = isPublic;
      }

      if (tags != null) {
        updateData['tags'] = tags;
      }

      // Upload new image if provided
      if (image != null) {
        final storageRef = _storage
            .ref()
            .child('group_chat_images')
            .child('${groupId}_${DateTime.now().millisecondsSinceEpoch}');

        final uploadTask = storageRef.putFile(image);
        final snapshot = await uploadTask.whenComplete(() {});
        final imageUrl = await snapshot.ref.getDownloadURL();

        updateData['imageUrl'] = imageUrl;
      }

      // Update the group chat
      await _firestore
          .collection('group_chats')
          .doc(groupId)
          .update(updateData);

      // Create a system message announcing the update
      final now = DateTime.now();
      final message = MessageModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        chatId: groupId,
        senderId: _userId,
        recipientId: '',
        text: 'Group information updated',
        timestamp: now,
        status: MessageStatus.sent,
        type: MessageType.system,
        isGroupMessage: true,
      );

      await _firestore.collection('messages').add(message.toJson());

      _fetchGroupChats();
    } catch (error) {
      rethrow;
    }
  }

  /// Marks all messages in a group chat as read for the current user
  Future<void> markGroupMessagesAsRead(String groupId) async {
    try {
      // Update the user's last read timestamp in the group
      final now = DateTime.now();
      await _firestore.collection('group_chats').doc(groupId).update({
        'members.$_userId.lastReadAt': now,
      });

      _fetchGroupChats();
    } catch (error) {
      rethrow;
    }
  }

  /// Mutes or unmutes a group chat for the current user
  Future<void> setGroupMuted(String groupId, bool isMuted) async {
    try {
      await _firestore.collection('group_chats').doc(groupId).update({
        'members.$_userId.isMuted': isMuted,
      });

      _fetchGroupChats();
    } catch (error) {
      rethrow;
    }
  }

  /// Leaves a group chat
  Future<void> leaveGroup(String groupId) async {
    try {
      await removeMemberFromGroup(groupId, _userId);
    } catch (error) {
      rethrow;
    }
  }

  /// Deletes a group chat (admin only)
  Future<void> deleteGroup(String groupId) async {
    try {
      final groupDoc =
          await _firestore.collection('group_chats').doc(groupId).get();

      if (!groupDoc.exists) {
        throw Exception('Group chat not found');
      }

      final group =
          GroupChatModel.fromJson({...groupDoc.data()!, 'id': groupDoc.id});

      // Check if the current user has permission to delete the group
      if (!group.isUserAdmin(_userId)) {
        throw Exception('You do not have permission to delete this group');
      }

      // Delete all messages in the group
      final messagesSnapshot = await _firestore
          .collection('messages')
          .where('chatId', isEqualTo: groupId)
          .get();

      final batch = _firestore.batch();
      for (final doc in messagesSnapshot.docs) {
        batch.delete(doc.reference);
      }

      // Delete the group chat
      batch.delete(_firestore.collection('group_chats').doc(groupId));

      await batch.commit();

      _fetchGroupChats();
    } catch (error) {
      rethrow;
    }
  }
}

/// Provider for group chats
final groupChatProvider =
    StateNotifierProvider<GroupChatNotifier, AsyncValue<List<GroupChatModel>>>(
        (ref) {
  final user = ref.watch(authStateProvider).user;
  if (user == null) {
    throw Exception('User must be logged in to access group chats');
  }

  final firestore = FirebaseFirestore.instance;
  final storage = FirebaseStorage.instance;

  return GroupChatNotifier(firestore, storage, user.id);
});

/// Stream provider for messages in a group chat
final groupChatMessagesProvider =
    StreamProvider.family<List<MessageModel>, String>((ref, groupId) {
  return FirebaseFirestore.instance
      .collection('messages')
      .where('chatId', isEqualTo: groupId)
      .where('isGroupMessage', isEqualTo: true)
      .orderBy('timestamp', descending: false)
      .snapshots()
      .map((snapshot) => snapshot.docs
          .map((doc) => MessageModel.fromJson(doc.data()))
          .toList());
});

/// Stream provider for a specific group chat
final groupChatDetailsProvider =
    StreamProvider.family<GroupChatModel?, String>((ref, groupId) {
  return FirebaseFirestore.instance
      .collection('group_chats')
      .doc(groupId)
      .snapshots()
      .map((doc) {
    if (!doc.exists) return null;
    return GroupChatModel.fromJson({...doc.data()!, 'id': doc.id});
  });
});

/// Provider for group chat members
final groupChatMembersProvider =
    FutureProvider.family<List<UserModel>, String>((ref, groupId) async {
  final groupDoc = await FirebaseFirestore.instance
      .collection('group_chats')
      .doc(groupId)
      .get();

  if (!groupDoc.exists) {
    return [];
  }

  final group =
      GroupChatModel.fromJson({...groupDoc.data()!, 'id': groupDoc.id});
  final memberIds = group.members.keys.toList();

  if (memberIds.isEmpty) {
    return [];
  }

  // Fetch user data for each member (in batches of 10 due to Firestore limitations)
  final members = <UserModel>[];
  for (var i = 0; i < memberIds.length; i += 10) {
    final end = (i + 10 < memberIds.length) ? i + 10 : memberIds.length;
    final batch = memberIds.sublist(i, end);

    final userDocs = await FirebaseFirestore.instance
        .collection('users')
        .where(FieldPath.documentId, whereIn: batch)
        .get();

    members.addAll(
      userDocs.docs
          .map((doc) => UserModel.fromJson({...doc.data(), 'id': doc.id}))
          .toList(),
    );
  }

  return members;
});

/// Stream provider for public group chats
final publicGroupChatsProvider = StreamProvider<List<GroupChatModel>>((ref) {
  return FirebaseFirestore.instance
      .collection('group_chats')
      .where('isPublic', isEqualTo: true)
      .orderBy('lastMessageAt', descending: true)
      .limit(20)
      .snapshots()
      .map((snapshot) => snapshot.docs
          .map((doc) => GroupChatModel.fromJson({...doc.data(), 'id': doc.id}))
          .toList());
});
