import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/translation/pronunciation_model.dart';
import 'package:culture_connect/providers/pronunciation_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';

/// A screen for pronunciation settings
class PronunciationSettingsScreen extends ConsumerWidget {
  /// Creates a new pronunciation settings screen
  const PronunciationSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final usePronunciationGuidance =
        ref.watch(usePronunciationGuidanceProvider);
    final useIpaNotation = ref.watch(useIpaNotationProvider);
    final useSimplifiedPhonetics = ref.watch(useSimplifiedPhoneticsProvider);
    final useSyllableBreakdown = ref.watch(useSyllableBreakdownProvider);
    final showDifficultOnly = ref.watch(showDifficultOnlyProvider);
    final autoPlayPronunciation = ref.watch(autoPlayPronunciationProvider);

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Pronunciation Settings',
        showBackButton: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Introduction
            const Text(
              'Pronunciation Guidance',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),

            const SizedBox(height: 8),

            const Text(
              'Pronunciation guidance helps you correctly pronounce words and phrases in different languages. This feature provides phonetic representations, audio pronunciations, and tips to improve your speaking skills.',
              style: TextStyle(
                fontSize: 14,
                color: AppTheme.textSecondaryColor,
              ),
            ),

            const SizedBox(height: 24),

            // Main settings
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'General Settings',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Pronunciation guidance toggle
                    SwitchListTile(
                      title: const Text(
                        'Enable Pronunciation Guidance',
                        style: TextStyle(
                          fontSize: 16,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      subtitle: const Text(
                        'Show pronunciation guides for translations',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      value: usePronunciationGuidance,
                      onChanged: (value) {
                        ref
                            .read(pronunciationNotifierProvider.notifier)
                            .setUsePronunciationGuidance(value);
                      },
                      activeColor: Colors.teal,
                    ),

                    // Auto-play pronunciation toggle
                    SwitchListTile(
                      title: const Text(
                        'Auto-Play Pronunciation',
                        style: TextStyle(
                          fontSize: 16,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      subtitle: const Text(
                        'Automatically play audio pronunciation when available',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      value: autoPlayPronunciation,
                      onChanged: usePronunciationGuidance
                          ? (value) {
                              ref
                                  .read(pronunciationNotifierProvider.notifier)
                                  .setAutoPlayPronunciation(value);
                            }
                          : null,
                      activeColor: Colors.teal,
                    ),

                    // Show difficult only toggle
                    SwitchListTile(
                      title: const Text(
                        'Focus on Difficult Pronunciations',
                        style: TextStyle(
                          fontSize: 16,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      subtitle: const Text(
                        'Highlight words and phrases that are difficult to pronounce',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      value: showDifficultOnly,
                      onChanged: usePronunciationGuidance
                          ? (value) {
                              ref
                                  .read(pronunciationNotifierProvider.notifier)
                                  .setShowDifficultOnly(value);
                            }
                          : null,
                      activeColor: Colors.teal,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Pronunciation guide types
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Pronunciation Guide Types',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),

                    const SizedBox(height: 16),

                    // IPA notation toggle
                    SwitchListTile(
                      title: const Text(
                        'IPA Notation',
                        style: TextStyle(
                          fontSize: 16,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      subtitle: const Text(
                        'Show International Phonetic Alphabet notation',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      value: useIpaNotation,
                      onChanged: usePronunciationGuidance
                          ? (value) {
                              ref
                                  .read(pronunciationNotifierProvider.notifier)
                                  .setUseIpaNotation(value);
                            }
                          : null,
                      activeColor: Colors.teal,
                    ),

                    // Simplified phonetics toggle
                    SwitchListTile(
                      title: const Text(
                        'Simplified Phonetics',
                        style: TextStyle(
                          fontSize: 16,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      subtitle: const Text(
                        'Show simplified phonetic spelling',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      value: useSimplifiedPhonetics,
                      onChanged: usePronunciationGuidance
                          ? (value) {
                              ref
                                  .read(pronunciationNotifierProvider.notifier)
                                  .setUseSimplifiedPhonetics(value);
                            }
                          : null,
                      activeColor: Colors.teal,
                    ),

                    // Syllable breakdown toggle
                    SwitchListTile(
                      title: const Text(
                        'Syllable Breakdown',
                        style: TextStyle(
                          fontSize: 16,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      subtitle: const Text(
                        'Show breakdown by syllables with stress marks',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      value: useSyllableBreakdown,
                      onChanged: usePronunciationGuidance
                          ? (value) {
                              ref
                                  .read(pronunciationNotifierProvider.notifier)
                                  .setUseSyllableBreakdown(value);
                            }
                          : null,
                      activeColor: Colors.teal,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Guide types
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'About Pronunciation Guide Types',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ...PronunciationGuideType.values.map((type) {
                      return _buildGuideTypeItem(type);
                    }).toList(),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Difficulty levels
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Difficulty Levels',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ...PronunciationDifficulty.values.map((difficulty) {
                      return _buildDifficultyLevelItem(difficulty);
                    }).toList(),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Clear cache button
            Center(
              child: ElevatedButton.icon(
                onPressed: () {
                  ref.read(pronunciationNotifierProvider.notifier).clearCache();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Pronunciation cache cleared'),
                      duration: Duration(seconds: 2),
                    ),
                  );
                },
                icon: const Icon(Icons.delete_outline),
                label: const Text('Clear Pronunciation Cache'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build a guide type item
  Widget _buildGuideTypeItem(PronunciationGuideType type) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.teal.withAlpha(26),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              type.icon,
              size: 24,
              color: Colors.teal,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  type.displayName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  type.description,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build a difficulty level item
  Widget _buildDifficultyLevelItem(PronunciationDifficulty difficulty) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: difficulty.color.withAlpha(26),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              difficulty.icon,
              size: 24,
              color: difficulty.color,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  difficulty.displayName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _getDifficultyLevelDescription(difficulty),
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Get the description for a difficulty level
  String _getDifficultyLevelDescription(PronunciationDifficulty difficulty) {
    switch (difficulty) {
      case PronunciationDifficulty.veryEasy:
        return 'Sounds that are very similar to English or are intuitive to pronounce.';
      case PronunciationDifficulty.easy:
        return 'Sounds that are somewhat similar to English with minor differences.';
      case PronunciationDifficulty.moderate:
        return 'Sounds that require some practice but are achievable with effort.';
      case PronunciationDifficulty.difficult:
        return 'Sounds that are significantly different from English and require dedicated practice.';
      case PronunciationDifficulty.veryDifficult:
        return 'Sounds that are not found in English and require extensive practice to master.';
    }
  }
}
