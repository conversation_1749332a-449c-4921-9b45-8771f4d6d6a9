import 'dart:io';

// Flutter imports
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

// Package imports
import 'package:culture_connect/models/translation/image_text_translation_model.dart';
import 'package:culture_connect/providers/image_text_translation_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/utils/language_utils.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';
import 'package:culture_connect/widgets/empty_state.dart';
import 'package:culture_connect/widgets/translation/translation_confidence_indicator.dart';
import 'package:culture_connect/widgets/translation/voice_translation_offline_indicator.dart';

/// Configuration for status indicators
class StatusConfig {
  final Color backgroundColor;
  final Color textColor;
  final IconData icon;
  final String label;

  const StatusConfig({
    required this.backgroundColor,
    required this.textColor,
    required this.icon,
    required this.label,
  });
}

/// A screen for viewing image text translation history
class ImageTextTranslationHistoryScreen extends ConsumerWidget {
  /// Creates a new image text translation history screen
  const ImageTextTranslationHistoryScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(imageTextTranslationProvider);
    final history = state.translationHistory;

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Translation History',
        showBackButton: true,
        actions: [
          // Clear history button
          if (history.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.delete_sweep),
              onPressed: () => _confirmClearHistory(context, ref),
              tooltip: 'Clear History',
            ),
        ],
      ),
      body: history.isEmpty
          ? EmptyState(
              icon: Iconsistory,
              title: 'No Translation History',
              message: 'Your image text translations will appear here',
              actionText: 'Translate an Image',
              onAction: () {
                Navigator.pop(context);
              },
            )
          : ListView.builder(
              padding: EdgeInsets.all(16),
              itemCount: history.length,
              itemBuilder: (context, index) {
                final translation = history[index];
                return _buildHistoryItem(context, ref, translation, index);
              },
            ),
    );
  }

  /// Build a history item
  Widget _buildHistoryItem(BuildContext context, WidgetRef ref,
      ImageTextTranslationModel translation, int index) {
    return Card(
      margin: EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      elevation: 2,
      child: InkWell(
        onTap: () {
          ref
              .read(imageTextTranslationProvider.notifier)
              .loadTranslation(translation.id);
          Navigator.pop(context);
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with languages and timestamp
              Row(
                children: [
                  // Source language
                  if (translation.sourceLanguage != null) ...[
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            LanguageUtils.getLanguageFlag(
                                translation.sourceLanguage!),
                            style: TextStyle(
                              fontSize: 14,
                            ),
                          ),
                          SizedBox(width: 4),
                          Text(
                            LanguageUtils.getLanguageName(
                                translation.sourceLanguage!),
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.black87,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Arrow
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 4),
                      child: Icon(
                        Icons.arrow_forward,
                        size: 12,
                        color: Colors.grey,
                      ),
                    ),
                  ],

                  // Target language
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor
                          .withAlpha(26), // 0.1 * 255 = 25.5, rounded to 26
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          LanguageUtils.getLanguageFlag(
                              translation.targetLanguage),
                          style: TextStyle(
                            fontSize: 14,
                          ),
                        ),
                        SizedBox(width: 4),
                        Text(
                          LanguageUtils.getLanguageName(
                              translation.targetLanguage),
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),

                  const Spacer(),

                  // Timestamp
                  Text(
                    _formatTimestamp(translation.timestamp),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),

              SizedBox(height: 12),

              // Image and text preview
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Image thumbnail
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: SizedBox(
                      width: 80,
                      height: 80,
                      child: FutureBuilder<bool>(
                        future: translation.imageExists(),
                        builder: (context, snapshot) {
                          if (snapshot.connectionState ==
                              ConnectionState.waiting) {
                            return const Center(
                                child: CircularProgressIndicator());
                          }

                          if (snapshot.data == true) {
                            return Image.file(
                              File(translation.imagePath),
                              fit: BoxFit.cover,
                            );
                          } else {
                            return Container(
                              color: Colors.grey[300],
                              child: Center(
                                child: Icon(
                                  Icons.image_not_supported,
                                  color: Colors.grey[600],
                                  size: 24,
                                ),
                              ),
                            );
                          }
                        },
                      ),
                    ),
                  ),

                  SizedBox(width: 12),

                  // Text preview
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Status indicator
                        _buildStatusIndicator(translation),

                        SizedBox(height: 8),

                        // Original text preview
                        if (translation.recognizedText != null) ...[
                          Text(
                            'Original:',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey[600],
                            ),
                          ),
                          Text(
                            translation.recognizedText!,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.black87,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          SizedBox(height: 4),
                        ],

                        // Translated text preview
                        if (translation.translatedText != null) ...[
                          Text(
                            'Translation:',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: AppTheme.primaryColor,
                            ),
                          ),
                          Text(
                            translation.translatedText!,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.black87,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),

              SizedBox(height: 12),

              // Footer with actions
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  // Offline indicator
                  if (translation.isOfflineTranslation)
                    VoiceTranslationOfflineIndicator(
                      isOffline: true,
                      confidence: translation.confidence?.overallScore ?? 1.0,
                      size: 16,
                      showLabel: false,
                    ),

                  SizedBox(width: 8),

                  // Confidence indicator
                  if (translation.confidence != null)
                    TranslationConfidenceIndicator(
                      confidence: translation.confidence!,
                      showLabel: false,
                      compact: true,
                    ),

                  const Spacer(),

                  // Favorite button
                  IconButton(
                    icon: Icon(
                      translation.isFavorite
                          ? Icons.favorite
                          : Icons.favorite_border,
                      color: translation.isFavorite ? Colors.red : null,
                      size: 20,
                    ),
                    onPressed: () {
                      ref
                          .read(imageTextTranslationProvider.notifier)
                          .toggleFavorite(translation.id);
                    },
                    tooltip: translation.isFavorite
                        ? 'Remove from favorites'
                        : 'Add to favorites',
                  ),

                  // Delete button
                  IconButton(
                    icon: Icon(
                      Icons.delete,
                      size: 20,
                    ),
                    onPressed: () =>
                        _confirmDeleteTranslation(context, ref, translation),
                    tooltip: 'Delete',
                  ),
                ],
              ),
            ],
          ),
        ),
      ).animate(delay: (index * 100).ms).fadeIn(duration: 300.ms).slideY(
          begin: 0.1, end: 0, duration: 300.ms, curve: Curves.easeOutQuad),
    );
  }

  /// Build a status indicator
  Widget _buildStatusIndicator(ImageTextTranslationModel translation) {
    // Define status configurations
    final StatusConfig config = _getStatusConfig(translation.status);

    // Build the status indicator
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: config.backgroundColor,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Status icon or progress indicator
          if (translation.status == ImageTextTranslationStatus.inProgress)
            SizedBox(
              width: 12,
              height: 12,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
              ),
            )
          else
            Icon(
              config.icon,
              size: 12,
              color: config.textColor,
            ),

          SizedBox(width: 4),

          // Status text
          Text(
            config.label,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: config.textColor,
            ),
          ),
        ],
      ),
    );
  }

  /// Get status configuration based on status
  StatusConfig _getStatusConfig(ImageTextTranslationStatus status) {
    switch (status) {
      case ImageTextTranslationStatus.inProgress:
        return StatusConfig(
          backgroundColor: Colors.blue[100]!,
          textColor: Colors.blue[800]!,
          icon: Iconsefresh,
          label: 'In Progress',
        );
      case ImageTextTranslationStatus.completed:
        return StatusConfig(
          backgroundColor: Colors.green[100]!,
          textColor: Colors.green[800]!,
          icon: Icons.check_circle,
          label: 'Completed',
        );
      case ImageTextTranslationStatus.failed:
        return StatusConfig(
          backgroundColor: Colorsed[100]!,
          textColor: Colorsed[800]!,
          icon: Icons.error,
          label: 'Failed',
        );
      case ImageTextTranslationStatus.pending:
        return StatusConfig(
          backgroundColor: Colors.orange[100]!,
          textColor: Colors.orange[800]!,
          icon: Iconsourglass_empty,
          label: 'Pending',
        );
    }
  }

  /// Confirm deleting a translation
  void _confirmDeleteTranslation(BuildContext context, WidgetRef ref,
      ImageTextTranslationModel translation) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Translation'),
        content: const Text(
            'Are you sure you want to delete this translation? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ref
                  .read(imageTextTranslationProvider.notifier)
                  .deleteTranslation(translation.id);
            },
            style: TextButton.styleFrom(
              foregroundColor: Colorsed,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  /// Confirm clearing history
  void _confirmClearHistory(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear History'),
        content: const Text(
            'Are you sure you want to clear all translation history? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              refead(imageTextTranslationProvider.notifier).clearHistory();
            },
            style: TextButton.styleFrom(
              foregroundColor: Colorsed,
            ),
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  /// Format a timestamp
  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays == 0) {
      return DateFormat.jm().format(timestamp); // Today, show time
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return DateFormat.E().format(timestamp); // Weekday
    } else {
      return DateFormat.yMd().format(timestamp); // Full date
    }
  }
}
