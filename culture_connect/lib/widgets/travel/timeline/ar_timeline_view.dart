import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:flutter_animate/flutter_animate.dart';

import 'package:culture_connect/models/travel/timeline.dart';
import 'package:culture_connect/models/travel/timeline_event.dart';
import 'package:culture_connect/widgets/travel/timeline/ar_timeline_event_card.dart';
import 'package:culture_connect/widgets/travel/timeline/timeline_day_header.dart';

/// A provider for the AR content filter
final timelineARFilterProvider = StateProvider<bool>((ref) => false);

/// A widget for displaying a timeline with AR integration
class ARTimelineView extends ConsumerStatefulWidget {
  /// The timeline to display
  final Timeline timeline;

  /// Whether to show only events with AR content
  final bool showARContentOnly;

  /// Callback when an event is tapped
  final Function(TimelineEvent)? onEventTap;

  /// Callback when AR content is tapped
  final Function(TimelineEvent)? onARContentTap;

  /// Creates a new AR timeline view
  const ARTimelineView({
    super.key,
    required this.timeline,
    this.showARContentOnly = false,
    this.onEventTap,
    this.onARContentTap,
  });

  @override
  ConsumerState<ARTimelineView> createState() => _ARTimelineViewState();
}

class _ARTimelineViewState extends ConsumerState<ARTimelineView> {
  /// The scroll controller
  final ScrollController _scrollController = ScrollController();

  /// The date format
  final DateFormat _dateFormat = DateFormat('yyyy-MM-dd');

  /// The selected date
  late DateTime _selectedDate;

  @override
  void initState() {
    super.initState();

    // Set the initial selected date to the start date
    _selectedDate = widget.timeline.startDate;
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// Build the header
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.timeline.theme.primaryColor.withAlpha(25),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.timeline.title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 8),

          // Description
          if (widget.timeline.description != null) ...[
            Text(
              widget.timeline.description!,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
              ),
            ),
            SizedBox(height: 8),
          ],

          // Date range
          Row(
            children: [
              Icon(
                Icons.date_range,
                size: 16,
                color: Colors.grey[700],
              ),
              SizedBox(width: 4),
              Text(
                '${DateFormat('MMM d, yyyy').format(widget.timeline.startDate)} - ${DateFormat('MMM d, yyyy').format(widget.timeline.endDate)}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[700],
                ),
              ),
            ],
          ),

          SizedBox(height: 8),

          // AR content stats
          Row(
            children: [
              Icon(
                Icons.view_in_ar,
                size: 16,
                color: Colors.blue,
              ),
              SizedBox(width: 4),
              Text(
                '${widget.timeline.eventsWithARContent.length} AR Experiences',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.blue,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build the date selector
  Widget _buildDateSelector() {
    // Get all dates in the range
    final dates = <DateTime>[];
    for (int i = 0; i <= widget.timeline.durationInDays - 1; i++) {
      dates.add(widget.timeline.startDate.add(Duration(days: i)));
    }

    return Container(
      height: 100,
      padding: EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: dates.length,
        itemBuilder: (context, index) {
          final date = dates[index];
          final isSelected =
              _dateFormat.format(date) == _dateFormat.format(_selectedDate);

          // Get events for this date
          final events = widget.timeline.getEventsForDate(date);

          // Check if there are AR events for this date
          final hasAREvents = events.any((event) => event.hasARContent);

          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedDate = date;
              });

              // Scroll to the selected date
              _scrollToDate(date);
            },
            child: Container(
              width: 60,
              margin: EdgeInsets.symmetric(horizontal: 4, vertical: 8),
              decoration: BoxDecoration(
                color: isSelected
                    ? widget.timeline.theme.primaryColor
                    : Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isSelected
                      ? widget.timeline.theme.primaryColor
                      : hasAREvents
                          ? Colors.blue
                          : Colors.grey[300]!,
                  width: hasAREvents ? 2 : 1,
                ),
                boxShadow: isSelected
                    ? [
                        BoxShadow(
                          color:
                              widget.timeline.theme.primaryColor.withAlpha(77),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ]
                    : null,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Month
                  Text(
                    DateFormat('MMM').format(date),
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: isSelected ? Colors.white : Colors.grey[700],
                    ),
                  ),

                  // Day
                  Text(
                    date.day.toString(),
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: isSelected ? Colors.white : Colors.black,
                    ),
                  ),

                  // AR indicator
                  if (hasAREvents) ...[
                    SizedBox(height: 4),
                    Icon(
                      Icons.view_in_ar,
                      size: 14,
                      color: isSelected ? Colors.white : Colors.blue,
                    ),
                  ],
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// Build the timeline content
  Widget _buildTimelineContent() {
    // Group events by date
    final eventsByDate = <String, List<TimelineEvent>>{};

    // Filter events if needed
    final filteredEvents = widget.showARContentOnly
        ? widget.timeline.eventsWithARContent
        : widget.timeline.events;

    for (final event in filteredEvents) {
      final dateString = _dateFormat.format(event.eventDate);
      if (!eventsByDate.containsKey(dateString)) {
        eventsByDate[dateString] = [];
      }
      eventsByDate[dateString]!.add(event);
    }

    // Sort dates
    final sortedDates = eventsByDate.keys.toList()..sort();

    // If no events, show empty state
    if (sortedDates.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              widget.showARContentOnly ? Icons.view_in_ar : Icons.event_busy,
              size: 64,
              color: Colors.grey[400],
            ),
            SizedBox(height: 16),
            Text(
              widget.showARContentOnly
                  ? 'No AR content in this timeline'
                  : 'No events in this timeline',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
            SizedBox(height: 8),
            Text(
              widget.showARContentOnly
                  ? 'Add AR content to your events to see them here'
                  : 'Add events to your timeline to get started',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    // Build timeline
    return ListView.builder(
      controller: _scrollController,
      padding: EdgeInsets.all(16),
      itemCount: sortedDates.length,
      itemBuilder: (context, index) {
        final dateString = sortedDates[index];
        final date = DateTime.parse(dateString);
        final events = eventsByDate[dateString]!;

        // Sort events by time
        events.sort((a, b) {
          if (a.eventTime == null && b.eventTime == null) return 0;
          if (a.eventTime == null) return -1;
          if (b.eventTime == null) return 1;

          final aMinutes = a.eventTime!.hour * 60 + a.eventTime!.minute;
          final bMinutes = b.eventTime!.hour * 60 + b.eventTime!.minute;
          return aMinutes.compareTo(bMinutes);
        });

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Day header
            TimelineDayHeader(
              date: date,
              theme: widget.timeline.theme,
            ),

            SizedBox(height: 8),

            // Events
            ...events.map((event) {
              return Padding(
                padding: EdgeInsets.only(bottom: 8),
                child: ARTimelineEventCard(
                  event: event,
                  theme: widget.timeline.theme,
                  onTap: () {
                    if (widget.onEventTap != null) {
                      widget.onEventTap!(event);
                    }
                  },
                  onARContentTap: () {
                    if (widget.onARContentTap != null && event.hasARContent) {
                      widget.onARContentTap!(event);
                    }
                  },
                ),
              ).animate().fadeIn(
                    duration: const Duration(milliseconds: 300),
                    delay: Duration(milliseconds: events.indexOf(event) * 50),
                  );
            }),

            SizedBox(height: 16),
          ],
        );
      },
    );
  }

  /// Scroll to a specific date
  void _scrollToDate(DateTime date) {
    final dateString = _dateFormat.format(date);

    // Group events by date
    final eventsByDate = <String, List<TimelineEvent>>{};

    // Filter events if needed
    final filteredEvents = widget.showARContentOnly
        ? widget.timeline.eventsWithARContent
        : widget.timeline.events;

    for (final event in filteredEvents) {
      final eventDateString = _dateFormat.format(event.eventDate);
      if (!eventsByDate.containsKey(eventDateString)) {
        eventsByDate[eventDateString] = [];
      }
      eventsByDate[eventDateString]!.add(event);
    }

    // Sort dates
    final sortedDates = eventsByDate.keys.toList()..sort();

    // Find the index of the date
    final index = sortedDates.indexOf(dateString);

    if (index != -1) {
      // Calculate the scroll position
      final position = index * 200; // Approximate height of a day section

      // Scroll to the position
      _scrollController.animateTo(
        position.toDouble(),
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Header
        Padding(
          padding: EdgeInsets.all(16),
          child: _buildHeader(),
        ),

        // Date selector
        _buildDateSelector(),

        // Timeline content
        Expanded(
          child: _buildTimelineContent(),
        ),
      ],
    );
  }
}
