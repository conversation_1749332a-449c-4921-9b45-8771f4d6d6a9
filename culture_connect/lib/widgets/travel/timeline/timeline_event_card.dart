import 'package:flutter/material.dart';

import 'package:culture_connect/models/travel/timeline.dart';
import 'package:culture_connect/models/travel/timeline_event.dart';
import 'package:culture_connect/widgets/travel/timeline/ar_content_badge.dart';

/// A card for displaying a timeline event
class TimelineEventCard extends StatefulWidget {
  /// The event to display
  final TimelineEvent event;

  /// The theme of the timeline
  final TimelineTheme theme;

  /// Whether to allow dragging
  final bool allowDrag;

  /// Callback when the card is tapped
  final VoidCallback? onTap;

  /// Callback when the card is long pressed
  final VoidCallback? onLongPress;

  /// Callback when the card is dragged and dropped
  final Function(DragEndDetails)? onDragEnd;

  /// Creates a new timeline event card
  const TimelineEventCard({
    super.key,
    required this.event,
    required this.theme,
    this.allowDrag = false,
    this.onTap,
    this.onLongPress,
    this.onDragEnd,
  });

  @override
  State<TimelineEventCard> createState() => _TimelineEventCardState();
}

class _TimelineEventCardState extends State<TimelineEventCard>
    with SingleTickerProviderStateMixin {
  /// The animation controller
  late AnimationController _animationController;

  /// The scale animation
  late Animation<double> _scaleAnimation;

  /// The elevation animation
  late Animation<double> _elevationAnimation;

  /// Whether the card is being dragged
  bool _isDragging = false;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
    );

    // Create animations
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _elevationAnimation = Tween<double>(begin: 1.0, end: 6.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Build the event time
  Widget _buildEventTime() {
    if (widget.event.eventTime == null) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: widget.theme.primaryColor.withAlpha(25),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        widget.event.formattedTimeRange ?? widget.event.formattedTime!,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: widget.theme.primaryColor,
        ),
      ),
    );
  }

  /// Build the event location
  Widget _buildEventLocation() {
    if (widget.event.location == null) {
      return const SizedBox.shrink();
    }

    return Row(
      children: [
        Icon(
          Icons.location_on,
          size: 14,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 4),
        Expanded(
          child: Text(
            widget.event.location!,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  /// Build the event duration
  Widget _buildEventDuration() {
    if (widget.event.durationMinutes == null) {
      return const SizedBox.shrink();
    }

    return Row(
      children: [
        Icon(
          Icons.access_time,
          size: 14,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 4),
        Text(
          widget.event.formattedDuration!,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  /// Build the event status
  Widget _buildEventStatus() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: widget.event.statusColor.withAlpha(25),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            widget.event.statusIcon,
            size: 12,
            color: widget.event.statusColor,
          ),
          const SizedBox(width: 4),
          Text(
            widget.event.status.toUpperCase(),
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: widget.event.statusColor,
            ),
          ),
        ],
      ),
    );
  }

  /// Build the event type
  Widget _buildEventType() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: widget.event.typeColor.withAlpha(25),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            widget.event.typeIcon,
            size: 12,
            color: widget.event.typeColor,
          ),
          const SizedBox(width: 4),
          Text(
            widget.event.eventType.toUpperCase(),
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: widget.event.typeColor,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final content = Card(
      elevation: _isDragging ? _elevationAnimation.value : 1.0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(
          color: widget.event.typeColor.withAlpha(77),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: widget.onTap,
        onLongPress: widget.onLongPress,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  // Event icon
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: widget.event.typeColor.withAlpha(25),
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Icon(
                        widget.event.typeIcon,
                        color: widget.event.typeColor,
                        size: 20,
                      ),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // Event title and time
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Title
                        Text(
                          widget.event.title,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),

                        const SizedBox(height: 4),

                        // Time
                        _buildEventTime(),
                      ],
                    ),
                  ),

                  // AR content badge
                  if (widget.event.hasARContent) ...[
                    const SizedBox(width: 8),
                    ARContentBadge(
                      arContentId: widget.event.arContentId,
                      size: 24,
                    ),
                  ],
                ],
              ),

              const SizedBox(height: 8),

              // Description
              if (widget.event.description != null) ...[
                Text(
                  widget.event.description!,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
              ],

              // Location and duration
              Row(
                children: [
                  // Location
                  Expanded(
                    child: _buildEventLocation(),
                  ),

                  // Duration
                  _buildEventDuration(),
                ],
              ),

              const SizedBox(height: 8),

              // Status and type
              Row(
                children: [
                  // Status
                  _buildEventStatus(),

                  const SizedBox(width: 8),

                  // Type
                  _buildEventType(),
                ],
              ),
            ],
          ),
        ),
      ),
    );

    if (!widget.allowDrag) {
      return content;
    }

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: child,
        );
      },
      child: LongPressDraggable<TimelineEvent>(
        data: widget.event,
        feedback: SizedBox(
          width: 300,
          child: Card(
            elevation: 6.0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
              side: BorderSide(
                color: widget.event.typeColor.withAlpha(77),
                width: 1,
              ),
            ),
            child: Padding(
              padding: EdgeInsets.all(12),
              child: Row(
                children: [
                  // Event icon
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: widget.event.typeColor.withAlpha(25),
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Icon(
                        widget.event.typeIcon,
                        color: widget.event.typeColor,
                        size: 20,
                      ),
                    ),
                  ),

                  SizedBox(width: 12),

                  // Event title and time
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Title
                        Text(
                          widget.event.title,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),

                        SizedBox(height: 4),

                        // Time
                        _buildEventTime(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        childWhenDragging: Opacity(
          opacity: 0.5,
          child: content,
        ),
        onDragStarted: () {
          if (!mounted) return;
          setState(() {
            _isDragging = true;
          });
          _animationController.forward();
        },
        onDragEnd: (details) {
          if (!mounted) return;
          setState(() {
            _isDragging = false;
          });
          _animationController.reverse();

          if (widget.onDragEnd != null) {
            // Pass the DraggableDetails directly
            // This is safe because we're just using it as a signal
            widget.onDragEnd!(details as DragEndDetails);
          }
        },
        onDraggableCanceled: (_, __) {
          if (!mounted) return;
          setState(() {
            _isDragging = false;
          });
          _animationController.reverse();
        },
        child: content,
      ),
    );
  }
}
