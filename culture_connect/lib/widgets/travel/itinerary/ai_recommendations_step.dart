import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/travel/ai_recommendation.dart';
import 'package:culture_connect/providers/travel/itinerary_providers.dart';
import 'package:culture_connect/theme/app_colors.dart';
import 'package:culture_connect/theme/app_text_styles.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/travel/itinerary/recommendation_card.dart';

/// A widget for displaying and selecting AI recommendations
class AIRecommendationsStep extends ConsumerStatefulWidget {
  /// The destination
  final String destination;

  /// The start date
  final DateTime startDate;

  /// The end date
  final DateTime endDate;

  /// The preferred item types
  final List<ItineraryItemType> preferredItemTypes;

  /// The preferred categories
  final List<String> preferredCategories;

  /// Callback when recommendations are accepted
  final Function(List<AIRecommendation>) onRecommendationsAccepted;

  /// Creates a new AI recommendations step
  const AIRecommendationsStep({
    super.key,
    required this.destination,
    required this.startDate,
    required this.endDate,
    required this.preferredItemTypes,
    required this.preferredCategories,
    required this.onRecommendationsAccepted,
  });

  @override
  ConsumerState<AIRecommendationsStep> createState() =>
      _AIRecommendationsStepState();
}

class _AIRecommendationsStepState extends ConsumerState<AIRecommendationsStep>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final List<AIRecommendation> _acceptedRecommendations = [];
  final List<AIRecommendation> _rejectedRecommendations = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Accept a recommendation
  void _acceptRecommendation(AIRecommendation recommendation) {
    setState(() {
      if (!_acceptedRecommendations.contains(recommendation)) {
        _acceptedRecommendations.add(recommendation);
      }
      _rejectedRecommendationsemove(recommendation);

      // Notify parent
      widget.onRecommendationsAccepted(_acceptedRecommendations);
    });

    // Send feedback to the AI
    _sendRecommendationFeedback(recommendation, true);
  }

  /// Reject a recommendation
  void _rejectRecommendation(AIRecommendation recommendation) {
    setState(() {
      _acceptedRecommendationsemove(recommendation);
      if (!_rejectedRecommendations.contains(recommendation)) {
        _rejectedRecommendations.add(recommendation);
      }

      // Notify parent
      widget.onRecommendationsAccepted(_acceptedRecommendations);
    });

    // Send feedback to the AI
    _sendRecommendationFeedback(recommendation, false);
  }

  /// Reset a recommendation
  void _resetRecommendation(AIRecommendation recommendation) {
    setState(() {
      _acceptedRecommendationsemove(recommendation);
      _rejectedRecommendationsemove(recommendation);

      // Notify parent
      widget.onRecommendationsAccepted(_acceptedRecommendations);
    });
  }

  /// Send feedback to the AI
  Future<void> _sendRecommendationFeedback(
      AIRecommendation recommendation, bool isAccepted) async {
    try {
      final aiRecommendationService = ref.read(aiRecommendationServiceProvider);
      await aiRecommendationService.sendRecommendationFeedback(
        userId:
            'current-user', // This would be the actual user ID in a real app
        recommendationId: recommendation.id,
        isAccepted: isAccepted,
      );
    } catch (e) {
      // Silently fail - this is just feedback
      debugPrint('Failed to send recommendation feedback: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get recommendations from provider
    final recommendationsAsync = ref.watch(aiRecommendationsProvider({
      'itemTypes': widget.preferredItemTypes,
      'limit': 20,
    }));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title
              const Text(
                'AI Recommendations',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),

              const SizedBox(height: 8),

              // Subtitle
              Text(
                'Personalized suggestions for your trip to ${widget.destination}',
                style: AppTextStyles.subtitle2.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),

        // Tab bar
        TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'All'),
            Tab(text: 'Accepted'),
            Tab(text: 'Rejected'),
          ],
          labelColor: AppColors.primary,
          unselectedLabelColor: Colors.grey,
          indicatorColor: AppColors.primary,
        ),

        // Tab content
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              // All recommendations
              _buildRecommendationsList(
                recommendationsAsync,
                (recommendation) => true,
              ),

              // Accepted recommendations
              _buildRecommendationsList(
                recommendationsAsync,
                (recommendation) =>
                    _acceptedRecommendations.contains(recommendation),
              ),

              // Rejected recommendations
              _buildRecommendationsList(
                recommendationsAsync,
                (recommendation) =>
                    _rejectedRecommendations.contains(recommendation),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build a list of recommendations
  Widget _buildRecommendationsList(
    AsyncValue<List<AIRecommendation>> recommendationsAsync,
    bool Function(AIRecommendation) filter,
  ) {
    return recommendationsAsync.when(
      data: (recommendations) {
        final filteredRecommendations = recommendations.where(filter).toList();

        if (filteredRecommendations.isEmpty) {
          return _buildEmptyState();
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: filteredRecommendations.length,
          itemBuilder: (context, index) {
            final recommendation = filteredRecommendations[index];
            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: RecommendationCard(
                recommendation: recommendation,
                isAccepted: _acceptedRecommendations.contains(recommendation),
                isRejected: _rejectedRecommendations.contains(recommendation),
                onAccept: () => _acceptRecommendation(recommendation),
                onReject: () => _rejectRecommendation(recommendation),
                onReset: () => _resetRecommendation(recommendation),
              ),
            );
          },
        );
      },
      loading: () => const Center(child: LoadingIndicator()),
      error: (error, stackTrace) => Center(
        child: ErrorView(
          error: error.toString(),
          onRetry: () {
            ref.invalidate(aiRecommendationsProvider);
          },
        ),
      ),
    );
  }

  /// Build an empty state
  Widget _buildEmptyState() {
    final isAcceptedTab = _tabController.index == 1;
    final isRejectedTab = _tabController.index == 2;

    IconData icon;
    String title;
    String message;

    if (isAcceptedTab) {
      icon = Icons.check_circle_outline;
      title = 'No Accepted Recommendations';
      message =
          'You haven\'t accepted any recommendations yet. Browse the "All" tab and accept recommendations you like.';
    } else if (isRejectedTab) {
      icon = Icons.cancel_outlined;
      title = 'No Rejected Recommendations';
      message = 'You haven\'t rejected any recommendations yet.';
    } else {
      icon = Icons.search;
      title = 'No Recommendations';
      message =
          'We couldn\'t find any recommendations matching your criteria. Try adjusting your preferences.';
    }

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 80,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: AppTextStyles.body2.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
