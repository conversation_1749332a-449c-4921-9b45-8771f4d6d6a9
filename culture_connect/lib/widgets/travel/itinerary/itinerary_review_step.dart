import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:culture_connect/models/travel/ai_recommendation.dart';
import 'package:culture_connect/providers/travel/itinerary_providers.dart';
import 'package:culture_connect/providers/travel/timeline_providers.dart';
import 'package:culture_connect/theme/app_colors.dart';
import 'package:culture_connect/theme/app_text_styles.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_view.dart';

/// Extension for ItineraryItemType
extension ItineraryItemTypeExtension on ItineraryItemType {
  /// Get the icon for the item type
  IconData get icon {
    switch (this) {
      case ItineraryItemType.accommodation:
        return Icons.hotel;
      case ItineraryItemType.transportation:
        return Icons.directions_car;
      case ItineraryItemType.activity:
        return Icons.local_activity;
      case ItineraryItemType.food:
        return Icons.restaurant;
      case ItineraryItemType.custom:
        return Icons.star;
    }
  }

  /// Get the color for the item type
  Color get color {
    switch (this) {
      case ItineraryItemType.accommodation:
        return Colors.indigo;
      case ItineraryItemType.transportation:
        return Colors.blue;
      case ItineraryItemType.activity:
        return Colors.green;
      case ItineraryItemType.food:
        return Colors.orange;
      case ItineraryItemType.custom:
        return Colors.purple;
    }
  }

  /// Get the display name for the item type
  String get displayName {
    switch (this) {
      case ItineraryItemType.accommodation:
        return 'Accommodations';
      case ItineraryItemType.transportation:
        return 'Transportation';
      case ItineraryItemType.activity:
        return 'Activities';
      case ItineraryItemType.food:
        return 'Food & Dining';
      case ItineraryItemType.custom:
        return 'Custom';
    }
  }
}

/// A widget for reviewing an itinerary before finalizing
class ItineraryReviewStep extends ConsumerWidget {
  /// The destination
  final String destination;

  /// The start date
  final DateTime startDate;

  /// The end date
  final DateTime endDate;

  /// The budget amount
  final double? budgetAmount;

  /// The budget currency
  final String budgetCurrency;

  /// The accepted recommendations
  final List<AIRecommendation> acceptedRecommendations;

  /// Creates a new itinerary review step
  const ItineraryReviewStep({
    super.key,
    required this.destination,
    required this.startDate,
    required this.endDate,
    this.budgetAmount,
    required this.budgetCurrency,
    required this.acceptedRecommendations,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dateFormat = DateFormat('MMM d, yyyy');

    // Get the current itinerary
    final currentItineraryAsync = ref.watch(currentItineraryProvider);

    return currentItineraryAsync.when(
      data: (itinerary) {
        if (itinerary == null) {
          return const Center(child: LoadingIndicator());
        }

        return SingleChildScrollView(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title
              const Text(
                'Review Your Itinerary',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),

              SizedBox(height: 8),

              // Subtitle
              Text(
                'Review and finalize your trip to $destination',
                style: AppTextStyles.subtitle2.copyWith(
                  color: Colors.grey[600],
                ),
              ),

              SizedBox(height: 24),

              // Itinerary summary card
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title
                      Text(
                        itinerary.title,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),

                      SizedBox(height: 16),

                      // Destination
                      _buildInfoRow(
                        Icons.location_on,
                        'Destination',
                        destination,
                      ),

                      SizedBox(height: 12),

                      // Dates
                      _buildInfoRow(
                        Icons.calendar_today,
                        'Dates',
                        '${dateFormat.format(startDate)} - ${dateFormat.format(endDate)}',
                      ),

                      SizedBox(height: 12),

                      // Duration
                      _buildInfoRow(
                        Icons.timelapse,
                        'Duration',
                        '${endDate.difference(startDate).inDays + 1} days',
                      ),

                      SizedBox(height: 12),

                      // Budget
                      if (budgetAmount != null) ...[
                        _buildInfoRow(
                          Icons.account_balance_wallet,
                          'Budget',
                          '$budgetCurrency ${budgetAmount!.toStringAsFixed(2)}',
                        ),
                        SizedBox(height: 12),
                      ],

                      // Accepted recommendations
                      _buildInfoRow(
                        Icons.recommend,
                        'Recommendations',
                        '${acceptedRecommendations.length} items',
                      ),
                    ],
                  ),
                ),
              ),

              SizedBox(height: 24),

              // Recommendations summary
              Text(
                'Accepted Recommendations',
                style: AppTextStyles.subtitle1.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),

              SizedBox(height: 16),

              // Group recommendations by type
              ...ItineraryItemType.values.map((type) {
                final typeRecommendations = acceptedRecommendations
                    .where((r) => r.item.type == type)
                    .toList();

                if (typeRecommendations.isEmpty) {
                  return const SizedBox.shrink();
                }

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Type header
                    Row(
                      children: [
                        Icon(
                          type.icon,
                          color: type.color,
                          size: 20,
                        ),
                        SizedBox(width: 8),
                        Text(
                          '${type.displayName} (${typeRecommendations.length})',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: type.color,
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: 8),

                    // Recommendations list
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: typeRecommendations.length,
                      itemBuilder: (context, index) {
                        final recommendation = typeRecommendations[index];
                        final item = recommendation.item;

                        return Card(
                          margin: EdgeInsets.only(bottom: 8),
                          elevation: 1,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: ListTile(
                            title: Text(
                              item.title,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (item.location != null) ...[
                                  Text(item.location!),
                                ],
                                if (item.formattedTimeRange != null) ...[
                                  Text(item.formattedTimeRange!),
                                ],
                              ],
                            ),
                            trailing: item.formattedPrice != null
                                ? Text(
                                    item.formattedPrice!,
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  )
                                : null,
                          ),
                        );
                      },
                    ),

                    SizedBox(height: 16),
                  ],
                );
              }),

              // No recommendations message
              if (acceptedRecommendations.isEmpty) ...[
                Container(
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.amber.withAlpha(25),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.amber),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Colors.amber[800],
                        size: 24,
                      ),
                      SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'No Recommendations Accepted',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.amber[800],
                              ),
                            ),
                            SizedBox(height: 8),
                            Text(
                              'You haven\'t accepted any recommendations yet. Your itinerary will be created with empty days that you can fill in later.',
                              style: TextStyle(fontSize: 14),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              SizedBox(height: 24),

              // Timeline button
              ElevatedButton.icon(
                onPressed: () {
                  _createAndViewTimeline(context, ref, itinerary);
                },
                icon: const Icon(Icons.timeline),
                label: const Text('View as Timeline'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  minimumSize: Size(double.infinity, 48),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),

              SizedBox(height: 24),

              // Next steps
              Container(
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color.fromRGBO(0, 122, 255, 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border:
                      Border.all(color: const Color.fromRGBO(0, 122, 255, 0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Next Steps',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'After creating your itinerary, you can:',
                      style: TextStyle(fontSize: 14),
                    ),
                    SizedBox(height: 8),
                    _buildNextStepItem(
                      'Customize your daily schedule',
                      Icons.edit_calendar,
                    ),
                    _buildNextStepItem(
                      'Add or remove activities',
                      Icons.add_circle_outline,
                    ),
                    _buildNextStepItem(
                      'Book accommodations and activities',
                      Icons.confirmation_number,
                    ),
                    _buildNextStepItem(
                      'Share your itinerary with travel companions',
                      Icons.share,
                    ),
                    _buildNextStepItem(
                      'View your itinerary as a visual timeline',
                      Icons.timeline,
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
      loading: () => const Center(child: LoadingIndicator()),
      error: (error, stackTrace) => Center(
        child: ErrorView(
          error: error.toString(),
          onRetry: () {
            ref.invalidate(currentItineraryProvider);
          },
        ),
      ),
    );
  }

  /// Build an information row
  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 20,
          color: AppColors.primary,
        ),
        SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
              SizedBox(height: 2),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build a next step item
  Widget _buildNextStepItem(String text, IconData icon) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: 18,
            color: AppColors.primary,
          ),
          SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  /// Create and view a timeline from the itinerary
  void _createAndViewTimeline(
      BuildContext context, WidgetRef ref, Itinerary itinerary) async {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            LoadingIndicator(),
            SizedBox(height: 16),
            Text('Creating timeline...'),
          ],
        ),
      ),
    );

    try {
      // Create a timeline from the itinerary
      await ref
          .read(currentTimelineProvider.notifier)
          .createTimelineFromItinerary(itinerary);

      // Get the timeline
      final timelineAsync = ref.read(currentTimelineProvider);

      if (context.mounted) {
        // Close the loading dialog
        Navigator.of(context).pop();

        // Navigate to the timeline screen
        timelineAsync.whenData((timeline) {
          if (timeline != null) {
            Navigator.of(context).pushNamed(
              '/timeline',
              arguments: {'timelineId': timeline.id},
            );
          }
        });
      }
    } catch (e) {
      if (context.mounted) {
        // Close the loading dialog
        Navigator.of(context).pop();

        // Show error dialog
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Error'),
            content: Text('Failed to create timeline: $e'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
    }
  }
}
